package com.mkefu.wechat.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.mkefu.common.response.Result;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;

/**
 * 代开发应用授权信息接口层
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
public interface WeChatThirdPartyAuthInfoService extends IService<WeChatThirdPartyAuthInfoEntity> {

    /**
     * 获取第三方应用凭证
     * @param entity 请求体
     * @return 响应体
     */
    void getThirdPartyCredentials(WeChatThirdPartyAuthInfoEntity entity);

    /**
     * 获取预授权码
     * @param suiteAccessToken 第三方应用凭证
     * @return 响应体
     */
    Result getPreAuthCode(String suiteAccessToken);

    /**
     * 获取企业永久授权码
     * @param suiteAccessToken 第三方应用凭证
     * @param temporaryAuthCode 用户扫二维码授权之后，微信回调给的临时授权码
     * @return 响应体
     */
    Result getPermanentCode(String suiteAccessToken, String temporaryAuthCode);
}
