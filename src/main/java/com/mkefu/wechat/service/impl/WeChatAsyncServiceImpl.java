package com.mkefu.wechat.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.util.XmlUtils;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;
import com.mkefu.wechat.entity.WeChatThirdPartyBaseInfoEntity;
import com.mkefu.wechat.mapper.WeChatThirdPartyAuthInfoMapper;
import com.mkefu.wechat.mapper.WeChatThirdPartyBaseInfoMapper;
import com.mkefu.wechat.service.WeChatAsyncService;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 微信异步处理服务实现类
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
@Service
public class WeChatAsyncServiceImpl implements WeChatAsyncService {

    private final XmlUtils xmlUtils;
    private final WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper;
    private final WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper;
    private final WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService;

    public WeChatAsyncServiceImpl(XmlUtils xmlUtils,
                                  WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper,
                                  WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper,
                                  WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService) {
        this.xmlUtils = xmlUtils;
        this.weChatThirdPartyAuthInfoMapper = weChatThirdPartyAuthInfoMapper;
        this.weChatThirdPartyBaseInfoMapper = weChatThirdPartyBaseInfoMapper;
        this.weChatThirdPartyAuthInfoService = weChatThirdPartyAuthInfoService;
    }

    /**
     * 异步处理infoType事件类型
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId 平台ID
     */
    @Override
    @Async("taskExecutor")
    public void handleInfoTypeAsync(String decryptedXml, String platFormId) {
        try {
            log.info("开始异步处理infoType事件，platFormId: {}", platFormId);
            handleInfoType(decryptedXml, platFormId);
            log.info("异步处理infoType事件完成，platFormId: {}", platFormId);
        } catch (Exception e) {
            log.error("异步处理infoType事件失败，platFormId: {}, error: {}", platFormId, e.getMessage(), e);
        }
    }

    /**
     * 处理infoType事件类型
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId 平台ID
     */
    private void handleInfoType(String decryptedXml, String platFormId) {
        // 解析XML拿到infoType事件类型
        String infoType = xmlUtils.getXmlInfoTypeValue(decryptedXml);
        if (infoType != null) {
            switch (infoType) {
                
                case CustomConstant.SUITE_TICKET:
                    // 处理推送suite_ticket事件
                    handleSuiteTicketInfoType(decryptedXml, platFormId);
                    break;
                    
                case CustomConstant.CREATE_AUTH:
                    // 授权通知事件
                    handlerCreateAuthInfoType(decryptedXml, platFormId);
                    break;
                    
                default:
                    log.warn("未知的infoType事件类型: {}", infoType);
                    break;
            }
        }
    }

    /**
     * 处理create_auth授权通知事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handlerCreateAuthInfoType(String decryptedXml, String platFormId) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "SuiteId", "AuthCode", "state", "ToUserName");
        
        WeChatThirdPartyAuthInfoEntity entity = weChatThirdPartyAuthInfoMapper
                .selectOne(new QueryWrapper<WeChatThirdPartyAuthInfoEntity>()
                .eq("suite_id", platFormId)
        );
        
        if (entity == null) {
            log.error("未找到对应的第三方授权信息，platFormId: {}", platFormId);
            return;
        }
        
        // 获取临时授权码
        String temporaryAuthCode = xmlValuesMap.getOrDefault("AuthCode", "");
        if (temporaryAuthCode.isEmpty()) {
            log.error("临时授权码为空，platFormId: {}", platFormId);
            return;
        }
        
        // 调用获取企业永久授权码接口
        try {
            weChatThirdPartyAuthInfoService.getPermanentCode(entity.getSuiteAccessToken(), temporaryAuthCode);
            log.info("成功获取企业永久授权码，platFormId: {}", platFormId);
        } catch (Exception e) {
            log.error("获取企业永久授权码失败，platFormId: {}, error: {}", platFormId, e.getMessage(), e);
        }
        
        // 构建不可变Map
        Map<String, String> xmlMap = Map.of(
                "SuiteId", entity.getSuiteId(),
                "AuthCode", temporaryAuthCode,
                "InfoType", CustomConstant.CREATE_AUTH,
                "TimeStamp", String.valueOf(System.currentTimeMillis() / 1000),
                "State", platFormId
        );
        String xmlString = xmlUtils.buildXmlWithCdata("xml", xmlMap);
        // 调用微信授权成功通知接口
        requestSuccessReceive(xmlString);
    }

    /**
     * 调用微信授权成功通知接口
     * @param xmlString XML字符串
     */
    private void requestSuccessReceive(String xmlString) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://127.0.0.1/suite/receive?msg_signature=3a7b08bb8e6dbce3c9671d6fdb69d15066227608&timestamp=1403610513&nonce=380320359")
                    .body(xmlString);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
                log.info("微信授权成功通知接口响应: {}", body);
            }
        } catch (Exception e) {
            log.error("调用微信授权成功通知接口失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理推送suite_ticket事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handleSuiteTicketInfoType(String decryptedXml, String platFormId) {
        // 解析XML拿到对应数据
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "SuiteId", "SuiteTicket");
        String suiteId = "";
        String suiteTicket = "";
        if (!xmlValuesMap.isEmpty()) {
            suiteId = xmlValuesMap.get("SuiteId");
            suiteTicket = xmlValuesMap.get("SuiteTicket");
        }
        // 查询数据库是否有已经存在的platFormId
        WeChatThirdPartyAuthInfoEntity existsEntity = weChatThirdPartyAuthInfoMapper.selectById(platFormId);
        WeChatThirdPartyAuthInfoEntity wechatThirdPartyAuthInfoEntity = new WeChatThirdPartyAuthInfoEntity();
        
        WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoMapper.selectById(existsEntity.getPlatFormId());
        if (ObjectUtils.isEmpty(baseInfoEntity)) {
            log.error("未找到对应的基础信息，platFormId: {}", platFormId);
            return;
        }
        
        if (ObjectUtils.isEmpty(existsEntity)) {
            // 如果没查到
            // platFormId、suiteId、suiteTicket、suiteSecret存入数据库
            wechatThirdPartyAuthInfoEntity.setPlatFormId(platFormId);
            wechatThirdPartyAuthInfoEntity.setSuiteId(suiteId);
            wechatThirdPartyAuthInfoEntity.setSuiteSecret(baseInfoEntity.getSuiteSecret());
            wechatThirdPartyAuthInfoEntity.setSuiteTicket(suiteTicket);
            wechatThirdPartyAuthInfoEntity.setCreateTime(new Date());
            weChatThirdPartyAuthInfoMapper.insert(wechatThirdPartyAuthInfoEntity);
        } else {
            // 如果查到就更新
            weChatThirdPartyAuthInfoMapper.update(new UpdateWrapper<WeChatThirdPartyAuthInfoEntity>()
                    .eq("plat_form_id", existsEntity.getPlatFormId())
                    .set("suite_id", existsEntity.getSuiteId())
                    .set("suite_secret", baseInfoEntity.getSuiteSecret())
                    .set("suite_ticket", suiteTicket)
                    .set("update_time", new Date())
            );
        }
        // 调用获取第三方企业凭证接口
        weChatThirdPartyAuthInfoService.getThirdPartyCredentials(wechatThirdPartyAuthInfoEntity);
    }
}
