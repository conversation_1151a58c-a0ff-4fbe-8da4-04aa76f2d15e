package com.mkefu.wechat.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mkefu.common.constant.CustomConstant;
import com.mkefu.common.util.WXBizJsonMsgCrypt;
import com.mkefu.common.util.WXBizMsgCrypt;
import com.mkefu.common.util.XmlUtils;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;
import com.mkefu.wechat.entity.WeChatThirdPartyBaseInfoEntity;
import com.mkefu.wechat.mapper.WeChatThirdPartyAuthInfoMapper;
import com.mkefu.wechat.mapper.WeChatThirdPartyBaseInfoMapper;
import com.mkefu.wechat.service.WeChatKfCallBackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 微信客服回调实现层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-17
 */
@Slf4j
@Service
public class WeChatKfCallBackServiceImpl implements WeChatKfCallBackService {

    private final WXBizJsonMsgCrypt wxBizJsonMsgCrypt;
    
    private final WXBizMsgCrypt wxBizMsgCrypt;
    
    private final WeChatThirdPartyAuthInfoServiceImpl weChatThirdPartyAuthInfoServiceImpl;
    
    private final WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper;
    
    private final WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper;
    
    private final XmlUtils xmlUtils;

    public WeChatKfCallBackServiceImpl(WXBizJsonMsgCrypt wxBizJsonMsgCrypt, WXBizMsgCrypt wxBizMsgCrypt, WeChatThirdPartyAuthInfoServiceImpl weChatThirdPartyAuthInfoServiceImpl, WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper, WeChatThirdPartyBaseInfoMapper weChatThirdPartyBaseInfoMapper, XmlUtils xmlUtils) {
        this.wxBizJsonMsgCrypt = wxBizJsonMsgCrypt;
        this.wxBizMsgCrypt = wxBizMsgCrypt;
        this.weChatThirdPartyAuthInfoServiceImpl = weChatThirdPartyAuthInfoServiceImpl;
        this.weChatThirdPartyAuthInfoMapper = weChatThirdPartyAuthInfoMapper;
        this.weChatThirdPartyBaseInfoMapper = weChatThirdPartyBaseInfoMapper;
        this.xmlUtils = xmlUtils;
    }

    /**
     * 校验密文和重新生成明文是否一致
     * @param methodType   方法类型
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param echoStr      加密的字符串，解密后需原样返回
     * @return 解密后的明文
     */
    @Override
    public String verifyDataCallBack(String methodType, String msgSignature, String timestamp, String nonce, String echoStr) {
        try {
            String sEchoStr = wxBizJsonMsgCrypt.verifyURL(msgSignature, timestamp, nonce, echoStr);
            if (CustomConstant.DATA_CALLBACK.equalsIgnoreCase(methodType)) {
                log.info("数据回调，verifyUrl echostr: {}", sEchoStr);
            } else {
                log.info("指令回调，verifyUrl echostr: {}", sEchoStr);
            }
            return sEchoStr;
        } catch (Exception e) {
            throw new IllegalArgumentException("执行回调方法失败：{" + e.getMessage() + "}");
        }
    }

    /**
     * 处理POST回调
     *
     * @param requestBody  请求体
     * @param msgSignature 加密签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @param platFormId
     * @return 响应体
     */
    @Override
    public String handlePostCallBack(String requestBody, String msgSignature, String timestamp, String nonce, String platFormId) {
        try {
            // 指令回调的接收方是服务商自己，所以用服务商的CorpID
            String decryptedXml = wxBizMsgCrypt.DecryptMsg(msgSignature, timestamp, nonce, requestBody);
            log.info("指令回调解密后内容: {}", decryptedXml);
            // 异步处理infoType事件类型
            CompletableFuture.runAsync(() -> handleInfoType(decryptedXml, platFormId));
            return "success";
        } catch (Exception e) {
            log.error("处理指令回调失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 异步处理infoType事件类型
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId 平台ID
     */
    private void handleInfoType(String decryptedXml, String platFormId) {
        // 解析XML拿到infoType事件类型
        String infoType = xmlUtils.getXmlValue(decryptedXml);
        if (infoType != null) {
            switch (infoType) {
                
                case CustomConstant.SUITE_TICKET:
                    // 处理推送suite_ticket事件
                    handleSuiteTicketInfoType(decryptedXml, platFormId);
                    break;
                    
                case CustomConstant.CREATE_AUTH:
                    // 授权通知事件
                    handlerCreateAuthInfoType(decryptedXml, platFormId);
                    break;
                    
                default:
                    break;
            }
        }
    }

    /**
     * 处理create_auth授权通知事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handlerCreateAuthInfoType(String decryptedXml, String platFormId) {
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "SuiteId", "AuthCode", "state", "ToUserName");
        
        WeChatThirdPartyAuthInfoEntity entity = weChatThirdPartyAuthInfoMapper
                .selectOne(new QueryWrapper<WeChatThirdPartyAuthInfoEntity>()
                .eq("suite_id", platFormId)
        );
        // 构建不可变Map
        Map<String, String> xmlMap = Map.of(
                "SuiteId", entity.getSuiteId(),
                "AuthCode", xmlValuesMap.getOrDefault("AuthCode", ""),
                "InfoType", CustomConstant.CREATE_AUTH,
                "TimeStamp", String.valueOf(System.currentTimeMillis() / 1000),
                "State", platFormId
        );
        String xmlString = xmlUtils.buildXmlWithCdata("xml", xmlMap);
        // 调用微信授权成功通知接口
        requestSuccessReceive(xmlString);
    }

    /**
     * 调用微信授权成功通知接口
     * @param xmlString
     */
    private void requestSuccessReceive(String xmlString) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://127.0.0.1/suite/receive?msg_signature=3a7b08bb8e6dbce3c9671d6fdb69d15066227608&timestamp=1403610513&nonce=380320359")
                    .body(xmlString);
            try (HttpResponse response = request.execute()) {
                String body = response.body();
            }
        } catch (Exception e) {
            log.error("<UNK>{}", e.getMessage(), e);
        }
    }

    /**
     * 处理推送suite_ticket事件
     *
     * @param decryptedXml 解密后的XML数据
     * @param platFormId   平台Id
     */
    private void handleSuiteTicketInfoType(String decryptedXml, String platFormId) {
        // 解析XML拿到对应数据
        Map<String, String> xmlValuesMap = xmlUtils.getXmlValues(decryptedXml, "SuiteId", "SuiteTicket");
        String suiteId = "";
        String suiteTicket = "";
        if (!xmlValuesMap.isEmpty()) {
            suiteId = xmlValuesMap.get("SuiteId");
            suiteTicket = xmlValuesMap.get("SuiteTicket");
        }
        // 查询数据库是否有已经存在的platFormId
        WeChatThirdPartyAuthInfoEntity existsEntity = weChatThirdPartyAuthInfoMapper.selectById(platFormId);
        WeChatThirdPartyAuthInfoEntity wechatThirdPartyAuthInfoEntity = new WeChatThirdPartyAuthInfoEntity();

        WeChatThirdPartyBaseInfoEntity baseInfoEntity = weChatThirdPartyBaseInfoMapper.selectById(existsEntity.getPlatFormId());
        if (ObjectUtils.isEmpty(existsEntity)) {
            // 如果没查到
            // platFormId、suiteId、suiteTicket、suiteSecret存入数据库
            wechatThirdPartyAuthInfoEntity.setPlatFormId(platFormId);
            wechatThirdPartyAuthInfoEntity.setSuiteId(suiteId);
            wechatThirdPartyAuthInfoEntity.setSuiteSecret(baseInfoEntity.getSuiteSecret());
            wechatThirdPartyAuthInfoEntity.setSuiteTicket(suiteTicket);
            wechatThirdPartyAuthInfoEntity.setCreateTime(new Date());
            weChatThirdPartyAuthInfoMapper.insert(wechatThirdPartyAuthInfoEntity);
        } else {
            // 如果查到就更新
            weChatThirdPartyAuthInfoMapper.update(new UpdateWrapper<WeChatThirdPartyAuthInfoEntity>()
                    .eq("plat_form_id", existsEntity.getPlatFormId())
                    .set("suite_id", existsEntity.getSuiteId())
                    .set("suite_secret", baseInfoEntity.getSuiteSecret())
                    .set("suite_ticket", suiteTicket)
                    .set("update_time", new Date())
            );
        }
        // 调用获取第三方企业凭证接口
        weChatThirdPartyAuthInfoServiceImpl.getThirdPartyCredentials(wechatThirdPartyAuthInfoEntity);
    }
}
