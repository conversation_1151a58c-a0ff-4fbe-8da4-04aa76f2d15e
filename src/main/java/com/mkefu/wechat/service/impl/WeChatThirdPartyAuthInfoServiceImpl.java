package com.mkefu.wechat.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mkefu.common.constant.ErrorCode;
import com.mkefu.common.exception.BusinessException;
import com.mkefu.common.response.Result;
import com.mkefu.wechat.dto.WeChatAppAuthResponseDto;
import com.mkefu.wechat.dto.WeChatGetPermanentCodeResponseDto;
import com.mkefu.wechat.dto.WeChatGetPreAuthCodeResponseDto;
import com.mkefu.wechat.entity.WeChatCustomerCompanyInfoEntity;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;
import com.mkefu.wechat.mapper.WeChatCustomerCompanyInfoMapper;
import com.mkefu.wechat.mapper.WeChatThirdPartyAuthInfoMapper;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;


/**
 * 微信应用授权实现层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
@Slf4j
@Service
public class WeChatThirdPartyAuthInfoServiceImpl extends ServiceImpl<WeChatThirdPartyAuthInfoMapper, WeChatThirdPartyAuthInfoEntity> implements WeChatThirdPartyAuthInfoService {
    
    private final ObjectMapper objectMapper;
    
    private final WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper;
    
    private final WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper;

    public WeChatThirdPartyAuthInfoServiceImpl(ObjectMapper objectMapper, WeChatThirdPartyAuthInfoMapper weChatThirdPartyAuthInfoMapper, WeChatCustomerCompanyInfoMapper weChatCustomerCompanyInfoMapper) {
        this.objectMapper = objectMapper;
        this.weChatThirdPartyAuthInfoMapper = weChatThirdPartyAuthInfoMapper;
        this.weChatCustomerCompanyInfoMapper = weChatCustomerCompanyInfoMapper;
    }

    /**
     * 获取第三方应用凭证
     * @param entity 请求体
     */
    @Override
    public void getThirdPartyCredentials(WeChatThirdPartyAuthInfoEntity entity) {
        // 如果第三方应用凭证不是空的，需要校验当前时间和有效期时间
        if (StringUtils.isNotBlank(entity.getSuiteAccessToken()) && !shouldRefreshData(entity.getCreateTime())) {
            return;
        }
        // 空的或者过期了，需要重新获取
        freshCredentials(entity);
    }

    /**
     * 获取新凭证进行新增或者更新
     * @param entity 请求体
     */
    private void freshCredentials(WeChatThirdPartyAuthInfoEntity entity) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token")
                    .body(objectMapper.writeValueAsString(entity));
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取第三方应用凭证接口：{}，入参：{}", request.getUrl(), objectMapper.writeValueAsString(entity));
                String respBody = response.body();
                log.info("微信响应结果：{}", respBody);
                if (StringUtils.isNotBlank(respBody)) {
                    WeChatAppAuthResponseDto appAuthResponseDto = objectMapper.readValue(respBody, WeChatAppAuthResponseDto.class);
                    entity.setSuiteAccessToken(appAuthResponseDto.getSuiteAccessToken());
                    entity.setSuiteAccessTokenExpiresIn(appAuthResponseDto.getExpiresIn());
                    // 获取预授权码
                    Result getPreAuthCodeResult = getPreAuthCode(appAuthResponseDto.getSuiteAccessToken());
                    if (getPreAuthCodeResult.isSuccess() && ObjectUtils.isNotEmpty(getPreAuthCodeResult.getData())) {
                        WeChatGetPreAuthCodeResponseDto preAuthCodeResponseDto = objectMapper.readValue(objectMapper.writeValueAsString(getPreAuthCodeResult.getData()), WeChatGetPreAuthCodeResponseDto.class);
                        entity.setSuitePreAuthCode(preAuthCodeResponseDto.getPreAuthCode());
                        entity.setSuitePreAuthCodeExpiresIn(appAuthResponseDto.getExpiresIn());
                        entity.setSuiteAuthUrl("https://work.weixin.qq.com/kf/third/auth/page?" + "suite_id" + "=" + entity.getSuiteId() + "&" + "pre_auth_code" + "=" + entity.getSuitePreAuthCode() + "&" + "state=" + entity.getPlatFormId());
                        // 新增或者更新数据库中的数据
                        entity.setUpdateTime(new Date());
                        entity.setCreateTime(new Date());
                        weChatThirdPartyAuthInfoMapper.insertOrUpdate(entity);
                        return;
                    }
                }
                throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_CREDENTIALS_FAILED.getCode(), "微信响应体为空");
            }
        } catch (BusinessException e) {
            log.error("获取第三方应用凭证失败：{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("获取第三方应用凭证发生未知异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_THIRD_PARTY_CREDENTIALS_FAILED.getCode(), ErrorCode.WECHAT_THIRD_PARTY_CREDENTIALS_FAILED.getDefaultMessage());
        }
    }

    /**
     * 获取预授权码
     * @param suiteAccessToken 第三方应用凭证
     * @return 响应体
     */
    @Override
    public Result getPreAuthCode(String suiteAccessToken) {
        try {
            HttpRequest request = HttpRequest
                    .get("https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code")
                    .form("suite_access_token", suiteAccessToken);
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取预授权码接口：{}", request.getUrl());
                String respBody = response.body();
                log.info("微信响应结果：{}", respBody);
                if (StringUtils.isNotBlank(respBody)) {
                    WeChatGetPreAuthCodeResponseDto dto = objectMapper.readValue(respBody, WeChatGetPreAuthCodeResponseDto.class);
                    return Result.success(dto);
                }
                throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), "微信响应体为空");
            }
        } catch (BusinessException e) {
            log.error("获取第三方应用凭证失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getDefaultMessage());
        } catch (Exception e) {
            log.error("获取第三方应用凭证发生未知异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getCode(), ErrorCode.WECHAT_PRE_AUTH_CODE_FAILED.getDefaultMessage());
        }
    }

    /**
     * 获取企业永久授权码
     * @param suiteAccessToken 第三方应用凭证
     * @param temporaryAuthCode 用户扫二维码授权之后，微信回调给的临时授权码
     * @return 响应体
     */
    @Override
    public Result getPermanentCode(String suiteAccessToken, String temporaryAuthCode) {
        try {
            HttpRequest request = HttpRequest
                    .post("https://qyapi.weixin.qq.com/cgi-bin/service/v2/get_permanent_code?suite_access_token=" + suiteAccessToken)
                    .body(temporaryAuthCode);
            try (HttpResponse response = request.execute()) {
                log.info("调用微信获取预授权码接口：{}，入参：{}", request.getUrl(),  temporaryAuthCode);
                String respBody = response.body();
                log.info("微信响应结果：{}", respBody);
                WeChatGetPermanentCodeResponseDto dto = objectMapper.readValue(respBody, WeChatGetPermanentCodeResponseDto.class);
                WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = convertDtoToEntity(dto);
                WeChatCustomerCompanyInfoEntity existsCustomerCompanyInfoEntity = weChatCustomerCompanyInfoMapper.selectOne(new QueryWrapper<WeChatCustomerCompanyInfoEntity>()
                        .eq("plat_form_id", dto.getState())
                );
                if (ObjectUtils.isNotEmpty(existsCustomerCompanyInfoEntity)) {
                    // 设置ID用于更新操作
                    customerCompanyInfoEntity.setPlatFormId(existsCustomerCompanyInfoEntity.getPlatFormId());
                    customerCompanyInfoEntity.setCreateTime(existsCustomerCompanyInfoEntity.getCreateTime());
                    customerCompanyInfoEntity.setUpdateTime(new Date());
                    weChatCustomerCompanyInfoMapper.updateById(customerCompanyInfoEntity);
                    return Result.success(customerCompanyInfoEntity);
                }
                // 设置创建时间
                customerCompanyInfoEntity.setCreateTime(new Date());
                if (weChatCustomerCompanyInfoMapper.insert(customerCompanyInfoEntity) > 0) {
                    return Result.success(customerCompanyInfoEntity);
                }
                throw new BusinessException(ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getCode(), ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getDefaultMessage());
            }
        } catch (BusinessException e) {
            log.error("获取企业永久授权码失败：{}", e.getMessage(), e);
            return Result.error(ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getCode(), ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getDefaultMessage());
        } catch (Exception e) {
            log.error("获取企业永久授权码发生未知异常：{}", e.getMessage(), e);
            return Result.error(ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getCode(), ErrorCode.WECHAT_GET_PERMANENT_CODE_FAILED.getDefaultMessage());
        }
    }
    
    /**
     * 将微信响应DTO转换为实体类
     * @param dto 微信响应DTO
     * @return 实体类
     */
    private WeChatCustomerCompanyInfoEntity convertDtoToEntity(WeChatGetPermanentCodeResponseDto dto) {
        WeChatCustomerCompanyInfoEntity customerCompanyInfoEntity = new WeChatCustomerCompanyInfoEntity();
        customerCompanyInfoEntity.setPermanentCode(dto.getPermanentCode());
        customerCompanyInfoEntity.setState(dto.getState());

        // 设置主键 - 使用state作为platFormId
        customerCompanyInfoEntity.setPlatFormId(dto.getState());
        // 授权方企业信息
        Map<String, Object> authCorpInfoMap = dto.getAuthCorpInfoMap();
        if (authCorpInfoMap != null) {
            customerCompanyInfoEntity.setCorpId(String.valueOf(authCorpInfoMap.getOrDefault("corpid", "")));
            customerCompanyInfoEntity.setCorpName(String.valueOf(authCorpInfoMap.getOrDefault("corp_name", "")));
        }

        // 授权管理员的信息
        Map<String, Object> authUserInfoMap = dto.getAuthUserInfoMap();
        if (authUserInfoMap != null) {
            customerCompanyInfoEntity.setUserId(String.valueOf(authUserInfoMap.getOrDefault("userid", "")));
            customerCompanyInfoEntity.setOpenUserid(String.valueOf(authUserInfoMap.getOrDefault("open_userid", "")));
            customerCompanyInfoEntity.setName(String.valueOf(authUserInfoMap.getOrDefault("name", "")));
            customerCompanyInfoEntity.setAvatar(String.valueOf(authUserInfoMap.getOrDefault("avatar", "")));
        }

        // 推广二维码安装相关信息
        Map<String, Object> registerCodeInfoMap = dto.getRegisterCodeInfoMap();
        if (registerCodeInfoMap != null) {
            customerCompanyInfoEntity.setRegisterCode(String.valueOf(registerCodeInfoMap.getOrDefault("register_code", "")));
            customerCompanyInfoEntity.setRegisterState(String.valueOf(registerCodeInfoMap.getOrDefault("state", "")));
            customerCompanyInfoEntity.setTemplateId(String.valueOf(registerCodeInfoMap.getOrDefault("template_id", "")));
        }
        return customerCompanyInfoEntity;
    }

    /**
     * 判断是否需要刷新数据
     * @param createTime 这条数据的创建时间
     * @return 是否需要刷新 true-需要刷新 false-不需要刷新
     */
    private boolean shouldRefreshData(Date createTime) {
        // 7200秒是代开发企业的应用凭证有效期时间
        long systemTimeStamp = System.currentTimeMillis() / 1000;
        long createTimeStamp = createTime.getTime() / 1000;
        boolean refreshDataFlag = systemTimeStamp - createTimeStamp > 7200;
        log.info("判断当前系统时间 减 这条数据的创建时间 是否大于 7200，结果：{}", refreshDataFlag);
        return refreshDataFlag;
    }
}
