package com.mkefu.wechat.controller;

import com.mkefu.wechat.service.WeChatKfCallBackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 微信客服回调Controller
 * <AUTHOR> 杨国锋
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat/kf")
@Schema(description = "微信客服回调Controller")
public class WeChatKfCallBackController {
    
    private final WeChatKfCallBackService weChatKfCallBackService;

    public WeChatKfCallBackController(WeChatKfCallBackService weChatKfCallBackService) {
        this.weChatKfCallBackService = weChatKfCallBackService;
    }

    @GetMapping("/dataCallBack/{platFormId}")
    @Operation(summary = "数据回调-验证通过后，此接口的POST方法将用于接收客户消息等实时数据")
    public String verifyDataCallBack(@RequestParam("msg_signature") String msgSignature, 
                                     @RequestParam("timestamp") String timestamp, 
                                     @RequestParam("nonce") String nonce, 
                                     @RequestParam("echostr") String echoStr,
                                     @PathVariable String platFormId) {
        try {
            return weChatKfCallBackService.verifyDataCallBack("dataCallBack", msgSignature, timestamp, nonce, echoStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("执行数据回调方法失败：{" + e.getMessage() + "}");
        }
    }

    @PostMapping("/dataCallBack/{platFormId}")
    @Operation(summary = "接收并处理来自 数据回调 的POST事件（如用户消息）。")
    public String handleDataCallBack(@RequestBody String requestBody,
                                     @RequestParam("msg_signature") String msgSignature,
                                     @RequestParam("timestamp") String timestamp,
                                     @RequestParam("nonce") String nonce,
                                     @PathVariable String platFormId) {
        try {
            return weChatKfCallBackService.handlePostCallBack(requestBody, msgSignature, timestamp, nonce, platFormId);
        } catch (Exception e) {
            throw new IllegalArgumentException("执行数据回调方法失败：{" + e.getMessage() + "}");
        }
    }

    @GetMapping("/commandCallBack/{platFormId}")
    @Operation(summary = "指令回调-验证通过后，此接口的POST方法将用于接收 suite_ticket 等系统指令")
    public String verifyCommandCallBack(@RequestParam("msg_signature") String msgSignature,
                                        @RequestParam("timestamp")String timestamp, 
                                        @RequestParam("nonce")String nonce,
                                        @RequestParam("echostr") String echoStr,
                                        @PathVariable String platFormId) {
        try {
            return weChatKfCallBackService.verifyDataCallBack("commandCallBack", msgSignature, timestamp, nonce, echoStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("执行数据回调方法失败：{" + e.getMessage() + "}");
        }
    }

    @PostMapping("/commandCallBack/{platFormId}")
    @Operation(summary = "接收并处理来自 指令回调URL 的POST事件（如 suite_ticket）")
    public String handleCommandCallBack(@RequestBody String requestBody,
                                        @RequestParam("msg_signature") String msgSignature,
                                        @RequestParam("timestamp") String timestamp,
                                        @RequestParam("nonce") String nonce,
                                        @PathVariable String platFormId) {
        try {
            log.info("POST请求msg_signature入参：{}", msgSignature);
            log.info("POST请求timestamp入参：{}", timestamp);
            log.info("POST请求nonce入参：{}", nonce);
            log.info("POST请求platFormId入参：{}", platFormId);
            log.info("POST请求Body入参：{}", requestBody);
            return weChatKfCallBackService.handlePostCallBack(requestBody, msgSignature, timestamp, nonce, platFormId);
        } catch (Exception e) {
            throw new IllegalArgumentException("执行数据回调方法失败：{" + e.getMessage() + "}");
        }
    }
}
