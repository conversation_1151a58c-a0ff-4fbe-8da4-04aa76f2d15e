package com.mkefu.wechat.controller;

import com.mkefu.common.response.Result;
import com.mkefu.wechat.entity.WeChatThirdPartyAuthInfoEntity;
import com.mkefu.wechat.service.WeChatThirdPartyAuthInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 代开发应用授权信息控制层
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-18
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat/appAuth")
@Schema(description = "代开发应用授权信息控制层")
public class WeChatThirdPartyAuthInfoController {
    
    private final WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService;

    public WeChatThirdPartyAuthInfoController(WeChatThirdPartyAuthInfoService weChatThirdPartyAuthInfoService) {
        this.weChatThirdPartyAuthInfoService = weChatThirdPartyAuthInfoService;
    }

//    @Operation(summary = "获取第三方应用凭证")
//    @PostMapping("/getThirdPartyCredentials")
//    public Result getThirdPartyCredentials(@RequestBody WeChatThirdPartyAuthInfoEntity entity) {
//        return weChatThirdPartyAuthInfoService.getThirdPartyCredentials(entity);
//    }

//    @Operation(summary = "获取预授权码")
//    @GetMapping("/getPreAuthCode")
//    public Result getPreAuthCode(@RequestParam("suite_access_token") String suiteAccessToken) {
//        return weChatThirdPartyAuthInfoService.getPreAuthCode(suiteAccessToken);
//    }
//
//    @Operation(summary = "获取企业永久授权码")
//    @PostMapping("/getPermanentCode")
//    public Result getPermanentCode(@RequestParam("suite_access_token") String suiteAccessToken,
//                                   @RequestBody String temporaryAuthCode) {
//        return weChatThirdPartyAuthInfoService.getPermanentCode(suiteAccessToken, temporaryAuthCode);
//    }
}
