package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 代开发企业配置页信息
 *
 * <AUTHOR> 杨国锋
 * @since 2025-06-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "代开发企业配置页信息", description = "WeChatThirdPartyBaseInfoEntity")
@TableName("we_chat_third_party_base_info")
public class WeChatThirdPartyBaseInfoEntity {
    
    @Schema(description = "平台ID")
    @TableId(value = "plat_form_id", type = IdType.NONE)
    private String platFormId;

    @Schema(description = "服务Id")
    @TableField("suite_id")
    @JsonProperty("suite_id")
    private String suiteId;

    @Schema(description = "服务密钥")
    @TableField("suite_secret")
    @JsonProperty("suite_secret")
    private String suiteSecret;

    @Schema(description = "服务Token")
    @TableField("suite_token")
    @JsonProperty("suite_token")
    private String suiteToken;

    @Schema(description = "服务解密密钥")
    @TableField("suite_encoding_aes_key")
    @JsonProperty("suite_secret")
    private String suiteEncodingAesKey;
    
}
