package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 被服务的企业信息表
 *
 * <AUTHOR> 杨国锋
 * @since 2025-06-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(name = "被服务的企业信息表", description = "WeChatCustomerCompanyInfoEntity")
public class WeChatCustomerCompanyInfoEntity extends BaseEntity implements Serializable {

    @Schema(description = "代开发企业平台Id")
    @TableId(value = "plat_form_id", type = IdType.NONE)
    private String platFormId;

    @Schema(description = "授权方企业微信Id")
    @TableField("permanent_code")
    @JsonProperty("permanent_code")
    private String permanentCode;

    @Schema(description = "被服务的企业微信Id")
    @TableField("corp_id")
    @JsonProperty("corpid")
    private String corpId;

    @Schema(description = "被服务的企业名称")
    @TableField("corp_name")
    @JsonProperty("corp_name")
    private String corpName;

    @Schema(description = "被服务的企业管理员的userid，可能为空")
    @TableField("user_id")
    @JsonProperty("userid")
    private String userId;

    @Schema(description = "被服务的企业管理员的open_userid，可能为空")
    @TableField("open_userid")
    @JsonProperty("open_userid")
    private String openUserid;

    @Schema(description = "被服务的企业管理员的name，可能为空")
    @TableField("name")
    private String name;

    @Schema(description = "被服务的企业管理员的头像url，可能为空")
    @TableField("avatar")
    private String avatar;

    @Schema(description = "被服务的企业注册码")
    @TableField("register_code")
    @JsonProperty("register_code")
    private String registerCode;

    @Schema(description = "被服务的企业推广包ID")
    @TableField("template_id")
    @JsonProperty("template_id")
    private String templateId;

    @Schema(description = "仅当获取注册码指定该字段时才返回")
    @TableField("register_state")
    private String registerState;

    @Schema(description = "安装应用时，扫码或者授权链接中带的state值。详见state说明")
    @TableField("state")
    private String state;
}
