package com.mkefu.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mkefu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 代开发企业应用授权信息实体类
 * <AUTHOR> 杨国锋
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("wechat_third_party_auth_info")
@Schema(name = "代开发企业应用授权信息实体类", description = "WeChatThirdPartyAuthInfoEntity")
public class WeChatThirdPartyAuthInfoEntity extends BaseEntity {

    @Schema(description = "代开发企业平台Id")
    @TableId(value = "plat_form_id", type = IdType.NONE)
    private String platFormId;
    
    @Schema(description = "代开发企业应用id")
    @TableField("suite_id")
    @JsonProperty("suite_id")
    private String suiteId;

    @Schema(description = "代开发企业微信后台推送的secret")
    @TableField(value = "suite_secret")
    @JsonProperty("suite_secret")
    private String suiteSecret;

    @Schema(description = "代开发企业微信后台推送的ticket")
    @TableField(value = "suite_ticket")
    @JsonProperty("suite_ticket")
    private String suiteTicket;

    @Schema(description = "代开发企业应用access_token,最长为512字节")
    @TableField(value = "suite_access_token")
    @JsonProperty("suite_access_token")
    private String suiteAccessToken;

    @Schema(description = "代开发企业应用access_token的有效期时间")
    @TableField(value = "suite_access_token_expires_in")
    @JsonProperty("suite_access_token_expires_in")
    private Integer suiteAccessTokenExpiresIn;

    @Schema(description = "代开发企业预授权码")
    @TableField("suite_pre_auth_code")
    @JsonProperty("pre_auth_code")
    private String suitePreAuthCode;

    @Schema(description = "代开发企业预授权码有效期时间")
    @TableField("suite_pre_auth_code_expires_in")
    @JsonProperty("pre_auth_code")
    private Integer suitePreAuthCodeExpiresIn;

    @Schema(description = "代开发企业授权链接URL")
    @TableField(value = "suite_auth_url")
    @JsonProperty("auth_url")
    private String suiteAuthUrl;

    @Schema(description = "被服务企业永久授权码")
    @TableField("permanent_code")
    @JsonProperty("permanent_code")
    private String permanentCode;
    
    @Schema(description = "错误码 0-正常")
    @JsonProperty("errcode")
    private Integer errorCode;

    @Schema(description = "错误描述")
    @JsonProperty("errmsg")
    private String errorMsg;

    @Schema(description = "有效期（秒）")
    @TableField("expires_in")
    @JsonProperty("expires_in")
    private Integer expiresIn;
}
