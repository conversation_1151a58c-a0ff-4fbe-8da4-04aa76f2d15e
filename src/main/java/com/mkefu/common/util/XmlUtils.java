package com.mkefu.common.util;

import cn.hutool.core.util.XmlUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.mkefu.common.constant.CustomConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.w3c.dom.CDATASection;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * XML工具
 *
 * <AUTHOR> Yang 杨国锋
 * @since 2025-06-19
 */
@Slf4j
@Component
public class XmlUtils {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 从XML字符串中获取指定标签的文本内容。
     * 这是一个私有辅助方法，供内部或单标签查询使用。
     *
     * @param decryptedXml XML格式的字符串。
     * @return 标签对应的文本内容（去除首尾空白），如果找不到标签、解析失败或内容为空则返回 {@code null}。
     */
    public String getXmlInfoTypeValue(String decryptedXml) {
        if (StringUtils.isBlank(decryptedXml)) {
            log.warn("getXmlValue: 输入的XML字符串为空。");
            return null;
        }
        try {
            // 2. 使用XmlUtil解析XML字符串为Document对象
            Document document = cn.hutool.core.util.XmlUtil.parseXml(decryptedXml);

            // 3. 构建XPath表达式：
            // "//" + tagName 表示在XML文档的任何位置查找名为 tagName 的元素。
            String xpathExpression = CustomConstant.DOUBLE_SLASH + CustomConstant.INFO_TYPE;

            // 4. 使用XmlUtil.getNode()通过XPath获取节点
            // 如果找不到匹配的节点，XmlUtil.getNode() 会返回 null，避免了 NPE。
            Node node = cn.hutool.core.util.XmlUtil.getNodeByXPath(xpathExpression, document);

            // 5. 判断节点是否存在并获取其文本内容
            if (node != null) {
                String textContent = node.getTextContent();
                // 进一步判断获取到的文本内容是否为空或只包含空白字符
                if (StringUtils.isNotBlank(textContent)) {
                    // 返回去除首尾空白的文本内容
                    return textContent.trim();
                } else {
                    // 找到了标签，但其内容为空白或为null
                    log.info("getXmlValue: 标签 '{}' 在XML中找到，但其内容为空白或null。XML: {}", CustomConstant.INFO_TYPE, decryptedXml);
                    return "";
                }
            } else {
                // 未找到匹配的标签
                log.info("getXmlValue: 未在XML中找到标签 '{}'。XML: {}", CustomConstant.INFO_TYPE, decryptedXml);
                return "";
            }
        } catch (Exception e) {
            // 6. 统一异常处理，记录错误日志并返回null
            // 捕获任何在XML解析（如格式错误）或XPath执行过程中可能发生的异常
            log.error("解析XML失败或获取标签 '{}' 值失败。XML: {}, 错误信息: {}", CustomConstant.INFO_TYPE, decryptedXml, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 从XML字符串中获取多个指定标签的文本内容。
     *
     * @param decryptedXml XML格式的字符串。
     * @param tagNames     要查找的标签名数组，例如 "SuiteId", "SuiteTicket", "TimeStamp"。
     * @return 一个Map，键为标签名，值为对应的文本内容（去除首尾空白）。
     * 如果标签不存在、解析失败或内容为空，对应的值将为 {@code null}。
     * 如果输入的XML字符串为空或标签名数组为空，返回空Map。
     */
    public Map<String, String> getXmlValues(String decryptedXml, String... tagNames) {
        if (StringUtils.isBlank(decryptedXml)) {
            log.warn("getXmlValues: 输入的XML字符串为空。");
            // 返回一个不可修改的空Map
            return Collections.emptyMap();
        }
        if (ObjectUtils.isEmpty(tagNames)) {
            log.warn("getXmlValues: 输入的标签名数组为空。");
            return Collections.emptyMap();
        }

        Document document;
        try {
            document = cn.hutool.core.util.XmlUtil.parseXml(decryptedXml);
        } catch (Exception e) {
            log.error("getXmlValues: 解析XML失败。XML: {}, 错误信息: {}", decryptedXml, e.getMessage(), e);
            return Collections.emptyMap();
        }

        Map<String, String> resultMap = HashMap.newHashMap(3);

        for (String tagName : tagNames) {
            if (StringUtils.isBlank(tagName)) {
                log.warn("getXmlValues: 标签名数组中包含空或空白标签名，已跳过。");
                // 跳过无效的标签名
                continue;
            }
            String xpathExpression = CustomConstant.DOUBLE_SLASH + tagName;
            try {
                Node node = cn.hutool.core.util.XmlUtil.getNodeByXPath(xpathExpression, document);
                if (node != null) {
                    String textContent = node.getTextContent();
                    if (StringUtils.isNotBlank(textContent)) {
                        resultMap.put(tagName, textContent.trim());
                    } else {
                        // 找到了标签，但其内容为空白或为null
                        log.info("getXmlValues: 标签 '{}' 在XML中找到，但其内容为空白或null。XML: {}", tagName, decryptedXml);
                        resultMap.put(tagName, null);
                    }
                } else {
                    // 未找到匹配的标签
                    log.info("getXmlValues: 未在XML中找到标签 '{}'。XML: {}", tagName, decryptedXml);
                    resultMap.put(tagName, null);
                }
            } catch (Exception e) {
                log.error("getXmlValues: 获取标签 '{}' 值失败。错误信息: {}", tagName, e.getMessage(), e);
                resultMap.put(tagName, null);
            }
        }
        return resultMap;
    }

    /**
     * 构造XML字符串
     *
     * @param rootName 根节点名称
     * @param elements 标签名-文本内容映射
     * @return 构造的XML字符串，失败返回null
     */
    public String buildXmlString(String rootName, Map<String, String> elements) {
        if (rootName == null || rootName.isEmpty() || elements == null || elements.isEmpty()) {
            throw new IllegalArgumentException("根节点名和元素不能为空");
        }
        try {
            Document doc = XmlUtil.createXml();
            Element root = doc.createElement(rootName);
            doc.appendChild(root);

            for (Map.Entry<String, String> entry : elements.entrySet()) {
                Element child = doc.createElement(entry.getKey());
                if (entry.getValue() != null) {
                    child.setTextContent(entry.getValue());
                }
                root.appendChild(child);
            }

            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将XML字符串转换为格式化的JSON字符串
     *
     * @param xmlStr XML字符串
     * @return JSON字符串，失败返回null
     */
    public static String convertXmlToJson(String xmlStr) {
        if (xmlStr == null || xmlStr.isEmpty()) {
            return null;
        }
        try {
            XmlMapper xmlMapper = new XmlMapper();
            JsonNode node = xmlMapper.readTree(xmlStr.getBytes(StandardCharsets.UTF_8));
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(node);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构造XML字符串并转换成JSON字符串返回
     *
     * @param rootName 根节点名
     * @param elements 标签名-文本内容映射
     * @return JSON字符串，失败返回null
     */
    public String buildXmlAndConvertToJson(String rootName, Map<String, String> elements) {
        String xmlStr = buildXmlString(rootName, elements);
        if (xmlStr == null) {
            return null;
        }
        return convertXmlToJson(xmlStr);
    }

    /**
     * 构造XML字符串，所有文本内容自动用CDATA包裹
     *
     * @param rootName 根节点名
     * @param elements 标签名-文本内容映射
     * @return 带CDATA的XML字符串
     */
    public String buildXmlWithCdata(String rootName, Map<String, String> elements) {
        if (rootName == null || rootName.isEmpty() || elements == null || elements.isEmpty()) {
            throw new IllegalArgumentException("根节点名和元素不能为空");
        }
        try {
            Document doc = XmlUtil.createXml();
            Element root = doc.createElement(rootName);
            doc.appendChild(root);

            for (Map.Entry<String, String> entry : elements.entrySet()) {
                Element child = doc.createElement(entry.getKey());
                String value = entry.getValue() == null ? "" : entry.getValue();
                CDATASection cdata = doc.createCDATASection(value);
                child.appendChild(cdata);
                root.appendChild(child);
            }

            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            return null;
        }
    }
}
