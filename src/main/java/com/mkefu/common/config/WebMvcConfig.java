package com.mkefu.common.config;

import com.mkefu.common.interceptor.SignatureInterceptor;
import com.mkefu.common.interceptor.TenantAuthCodeInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * WebMvc配置类，用于注册拦截器
 * <AUTHOR>
 * @since 2025-04-10
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final SignatureInterceptor signatureInterceptor;

    private final TenantAuthCodeInterceptor tenantAuthCodeInterceptor;

    public WebMvcConfig(SignatureInterceptor signatureInterceptor,
                        TenantAuthCodeInterceptor tenantAuthCodeInterceptor) {
        this.signatureInterceptor = signatureInterceptor;
        this.tenantAuthCodeInterceptor = tenantAuthCodeInterceptor;
    }

    /**
     * 注册拦截器
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册签名拦截器
        registry.addInterceptor(signatureInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/tenant/generateMethodSecretKey")
                .excludePathPatterns("/api/tenant/generateSignature")
                .excludePathPatterns("/api/wechat/**")
                .excludePathPatterns("/v3/api-docs/**");

        // 注册租户授权码拦截器
        registry.addInterceptor(tenantAuthCodeInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/tenant/generateMethodSecretKey")
                .excludePathPatterns("/v3/api-docs/**")
                .excludePathPatterns("/api/tenant/generateSignature")
                .excludePathPatterns("/api/wechat/**")
                .excludePathPatterns("/api/redis");
    }
}
