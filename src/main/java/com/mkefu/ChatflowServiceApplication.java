package com.mkefu;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动类
 * <AUTHOR>
 * @since 2025-04-10
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication
@EnableConfigurationProperties
@MapperScan(basePackages = "com.mkefu.*.mapper")
@ComponentScan(basePackages = {"com.mkefu.*"})
public class ChatflowServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(ChatflowServiceApplication.class, args);
	}

}
